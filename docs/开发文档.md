# 《电子木鱼 Digital Wooden Fish》

📄 功能说明文档（MVP 版本）
🗓️ 版本日期：2025-06-13
## 1️⃣ 产品定位

《电子木鱼 Digital Wooden Fish》是一款极简治愈系 App，用户可通过全屏点击来模拟“敲木鱼”动作，体验多种不同风格木鱼款式及衍生玩法，通过每日轻松点击，缓解压力、获得治愈感，培养每日仪式感。

---

## 2️⃣ 目标用户场景

* 上班学习间隙，点击放松
* 睡前静心，点击助眠
* 焦虑时，点击平复情绪
* 休闲时，点击积累成就解锁木鱼款式

---

## 3️⃣ 功能模块

### 3.1 主界面

| 元素     | 描述                         |
| ------ | -------------------------- |
| 点击区域   | 全屏区域，点击即触发敲木鱼              |
| 顶部状态栏  | 显示当前木鱼款式名 + 今日点击次数 + 总点击次数 |
| 右上角按钮  | 设置按钮、木鱼列表按钮                |
| 底部横幅广告 | 敲满 100 次今日自动隐藏广告，次日 0 点重置  |

---

### 3.2 点击交互逻辑

* 每次点击 → 播放当前木鱼动画 + 音效
* 今日点击数 +1
* 刷新计数器显示
* 达到 100 次 → 隐藏广告

---

### 3.3 木鱼款式

#### 📜 基础段位款式（仿英雄联盟风格）

* 基础木鱼（默认解锁）
* 青铜木鱼
* 白银木鱼
* 黄金木鱼
* 铂金木鱼
* 钻石木鱼
* 大师木鱼
* 王者木鱼
* 彩虹木鱼

#### 🌟 特别风格款式（玩法型）

* 羊跳栏杆
* 风铃木鱼
* 泡泡木鱼
* 外星木鱼

---

### 3.4 解锁机制

| 木鱼类型 | 解锁方式                |
| ---- | ------------------- |
| 基础木鱼 | 默认解锁                |
| 其他木鱼 | 按累计总点击数解锁（具体阈值后续设定） |

---

### 3.5 设置页

| 项目   | 描述        |
| ---- | --------- |
| 音效开关 | 默认开启，可关闭  |
| 振动开关 | 默认开启，可关闭  |
| 版本信息 | 当前 App 版本 |
| 联系我们 | 邮件/支持链接   |

---

### 3.6 木鱼列表页

* 网格/列表展示全部木鱼款式
* 已解锁 / 未解锁 状态显示
* 点击可切换当前使用木鱼（已解锁可用）

---

## 4️⃣ 数据持久化需求

* 今日点击数
* 总点击数
* 已解锁木鱼状态
* 当前选中木鱼款式
* 广告显示状态
* 设置页用户配置

---

## 5️⃣ 每日重置机制

* 每天 0 点自动重置：

  * 今日点击数 = 0
  * 广告状态 = 显示

---

## 6️⃣ 技术要求

* 单机应用，无需注册登录
* UI 极简，音效治愈系
* 高性能，保证点击动画/音效不卡顿
* 适配主流机型（安卓 / iOS）
* MVP 版暂不支持自定义木鱼，不接入社交功能
* 支持多语言版本（中 / 英，后续可扩展）

---

✅ **交付建议**：

1️⃣ 先开发主界面 + 点击体验
2️⃣ 同步开发木鱼管理 / 解锁机制
3️⃣ 设置页 / 列表页可并行开发
4️⃣ 广告逻辑 / 重置逻辑 最后接入测试


