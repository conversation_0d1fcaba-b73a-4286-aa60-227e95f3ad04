class WoodenFish {
  final String id;
  final String name;
  final String imagePath;
  final String soundPath;
  final int unlockThreshold;
  final WoodenFishType type;

  const WoodenFish({
    required this.id,
    required this.name,
    required this.imagePath,
    required this.soundPath,
    required this.unlockThreshold,
    required this.type,
  });

  bool isUnlocked(int totalClicks) {
    return totalClicks >= unlockThreshold;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'imagePath': imagePath,
      'soundPath': soundPath,
      'unlockThreshold': unlockThreshold,
      'type': type.toString(),
    };
  }

  factory WoodenFish.fromJson(Map<String, dynamic> json) {
    return WoodenFish(
      id: json['id'],
      name: json['name'],
      imagePath: json['imagePath'],
      soundPath: json['soundPath'],
      unlockThreshold: json['unlockThreshold'],
      type: WoodenFishType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
    );
  }
}

enum WoodenFishType {
  basic,    // 基础段位
  special,  // 特别风格
}