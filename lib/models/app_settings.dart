class AppSettings {
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String language;

  const AppSettings({
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.language = 'zh',
  });

  AppSettings copyWith({
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? language,
  }) {
    return AppSettings(
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      language: language ?? this.language,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'language': language,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      soundEnabled: json['soundEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      language: json['language'] ?? 'zh',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppSettings &&
        other.soundEnabled == soundEnabled &&
        other.vibrationEnabled == vibrationEnabled &&
        other.language == language;
  }

  @override
  int get hashCode {
    return soundEnabled.hashCode ^
        vibrationEnabled.hashCode ^
        language.hashCode;
  }
}