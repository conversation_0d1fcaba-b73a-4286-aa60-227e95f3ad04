import 'wooden_fish.dart';

class WoodenFishData {
  static final List<WoodenFish> allWoodenFish = [
    // 基础段位木鱼
    WoodenFish(
      id: 'basic_1',
      name: '普通木鱼',
      imagePath: 'assets/images/fish_basic_1.png',
      soundPath: 'assets/sounds/fish_basic_1.mp3',
      unlockThreshold: 0, // 默认解锁
      type: WoodenFishType.basic,
    ),
    WoodenFish(
      id: 'basic_2',
      name: '青铜木鱼',
      imagePath: 'assets/images/fish_basic_2.png',
      soundPath: 'assets/sounds/fish_basic_2.mp3',
      unlockThreshold: 100,
      type: WoodenFishType.basic,
    ),
    WoodenFish(
      id: 'basic_3',
      name: '白银木鱼',
      imagePath: 'assets/images/fish_basic_3.png',
      soundPath: 'assets/sounds/fish_basic_3.mp3',
      unlockThreshold: 500,
      type: WoodenFishType.basic,
    ),
    <PERSON>en<PERSON>ish(
      id: 'basic_4',
      name: '黄金木鱼',
      imagePath: 'assets/images/fish_basic_4.png',
      soundPath: 'assets/sounds/fish_basic_4.mp3',
      unlockThreshold: 1000,
      type: WoodenFishType.basic,
    ),
    WoodenFish(
      id: 'basic_5',
      name: '铂金木鱼',
      imagePath: 'assets/images/fish_basic_5.png',
      soundPath: 'assets/sounds/fish_basic_5.mp3',
      unlockThreshold: 2000,
      type: WoodenFishType.basic,
    ),
    WoodenFish(
      id: 'basic_6',
      name: '钻石木鱼',
      imagePath: 'assets/images/fish_basic_6.png',
      soundPath: 'assets/sounds/fish_basic_6.mp3',
      unlockThreshold: 5000,
      type: WoodenFishType.basic,
    ),
    
    // 特别风格木鱼
    WoodenFish(
      id: 'special_1',
      name: '彩虹木鱼',
      imagePath: 'assets/images/fish_special_1.png',
      soundPath: 'assets/sounds/fish_special_1.mp3',
      unlockThreshold: 3000,
      type: WoodenFishType.special,
    ),
    WoodenFish(
      id: 'special_2',
      name: '星空木鱼',
      imagePath: 'assets/images/fish_special_2.png',
      soundPath: 'assets/sounds/fish_special_2.mp3',
      unlockThreshold: 4000,
      type: WoodenFishType.special,
    ),
    WoodenFish(
      id: 'special_3',
      name: '火焰木鱼',
      imagePath: 'assets/images/fish_special_3.png',
      soundPath: 'assets/sounds/fish_special_3.mp3',
      unlockThreshold: 6000,
      type: WoodenFishType.special,
    ),
    WoodenFish(
      id: 'special_4',
      name: '冰霜木鱼',
      imagePath: 'assets/images/fish_special_4.png',
      soundPath: 'assets/sounds/fish_special_4.mp3',
      unlockThreshold: 8000,
      type: WoodenFishType.special,
    ),
    WoodenFish(
      id: 'special_5',
      name: '雷电木鱼',
      imagePath: 'assets/images/fish_special_5.png',
      soundPath: 'assets/sounds/fish_special_5.mp3',
      unlockThreshold: 10000,
      type: WoodenFishType.special,
    ),
    WoodenFish(
      id: 'special_6',
      name: '神圣木鱼',
      imagePath: 'assets/images/fish_special_6.png',
      soundPath: 'assets/sounds/fish_special_6.mp3',
      unlockThreshold: 15000,
      type: WoodenFishType.special,
    ),
  ];

  /// 根据ID获取木鱼
  static WoodenFish? getWoodenFishById(String id) {
    try {
      return allWoodenFish.firstWhere((fish) => fish.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取默认木鱼（第一个）
  static WoodenFish get defaultWoodenFish => allWoodenFish.first;

  /// 获取基础段位木鱼列表
  static List<WoodenFish> get basicWoodenFish {
    return allWoodenFish.where((fish) => fish.type == WoodenFishType.basic).toList();
  }

  /// 获取特别风格木鱼列表
  static List<WoodenFish> get specialWoodenFish {
    return allWoodenFish.where((fish) => fish.type == WoodenFishType.special).toList();
  }

  /// 根据点击次数获取已解锁的木鱼列表
  static List<WoodenFish> getUnlockedFish(int totalClicks) {
    return allWoodenFish.where((fish) => fish.isUnlocked(totalClicks)).toList();
  }

  /// 根据点击次数获取下一个可解锁的木鱼
  static WoodenFish? getNextUnlockableFish(int totalClicks) {
    final lockedFish = allWoodenFish
        .where((fish) => !fish.isUnlocked(totalClicks))
        .toList();
    
    if (lockedFish.isEmpty) return null;
    
    // 按解锁阈值排序，返回最接近的
    lockedFish.sort((a, b) => a.unlockThreshold.compareTo(b.unlockThreshold));
    return lockedFish.first;
  }

  /// 获取解锁进度信息
  static Map<String, dynamic> getUnlockProgress(int totalClicks) {
    final unlockedCount = getUnlockedFish(totalClicks).length;
    final totalCount = allWoodenFish.length;
    final nextFish = getNextUnlockableFish(totalClicks);
    
    return {
      'unlockedCount': unlockedCount,
      'totalCount': totalCount,
      'progress': unlockedCount / totalCount,
      'nextFish': nextFish,
      'nextFishRemaining': nextFish != null ? nextFish.unlockThreshold - totalClicks : 0,
    };
  }

  /// 检查是否有新解锁的木鱼
  static List<WoodenFish> getNewlyUnlockedFish(int oldClicks, int newClicks) {
    final oldUnlocked = getUnlockedFish(oldClicks).map((f) => f.id).toSet();
    final newUnlocked = getUnlockedFish(newClicks);
    
    return newUnlocked.where((fish) => !oldUnlocked.contains(fish.id)).toList();
  }
}