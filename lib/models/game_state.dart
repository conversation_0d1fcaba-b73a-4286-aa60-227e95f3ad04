class GameState {
  final int todayClicks;
  final int totalClicks;
  final String currentWoodenFishId;
  final DateTime lastPlayDate;
  final bool adHidden;

  GameState({
    this.todayClicks = 0,
    this.totalClicks = 0,
    this.currentWoodenFishId = 'basic',
    DateTime? lastPlayDate,
    this.adHidden = false,
  }) : lastPlayDate = lastPlayDate ?? DateTime.now();

  GameState._defaultDate() : 
    todayClicks = 0,
    totalClicks = 0,
    currentWoodenFishId = 'basic_1',
    adHidden = false,
    lastPlayDate = DateTime.now();

  GameState copyWith({
    int? todayClicks,
    int? totalClicks,
    String? currentWoodenFishId,
    DateTime? lastPlayDate,
    bool? adHidden,
  }) {
    return GameState(
      todayClicks: todayClicks ?? this.todayClicks,
      totalClicks: totalClicks ?? this.totalClicks,
      currentWoodenFishId: currentWoodenFishId ?? this.currentWoodenFishId,
      lastPlayDate: lastPlayDate ?? this.lastPlayDate,
      adHidden: adHidden ?? this.adHidden,
    );
  }

  // 增加点击次数
  GameState incrementClicks() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final lastDay = DateTime(lastPlayDate.year, lastPlayDate.month, lastPlayDate.day);
    
    // 检查是否需要重置今日计数
    final shouldReset = today.isAfter(lastDay);
    
    return GameState(
      todayClicks: shouldReset ? 1 : todayClicks + 1,
      totalClicks: totalClicks + 1,
      currentWoodenFishId: currentWoodenFishId,
      lastPlayDate: now,
      adHidden: shouldReset ? false : (todayClicks + 1 >= 100),
    );
  }

  // 检查是否需要重置每日数据
  GameState checkDailyReset() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final lastDay = DateTime(lastPlayDate.year, lastPlayDate.month, lastPlayDate.day);
    
    if (today.isAfter(lastDay)) {
      return GameState(
        todayClicks: 0,
        totalClicks: totalClicks,
        currentWoodenFishId: currentWoodenFishId,
        lastPlayDate: now,
        adHidden: false,
      );
    }
    
    return this;
  }

  Map<String, dynamic> toJson() {
    return {
      'todayClicks': todayClicks,
      'totalClicks': totalClicks,
      'currentWoodenFishId': currentWoodenFishId,
      'lastPlayDate': lastPlayDate.millisecondsSinceEpoch,
      'adHidden': adHidden,
    };
  }

  factory GameState.fromJson(Map<String, dynamic> json) {
    return GameState(
      todayClicks: json['todayClicks'] ?? 0,
      totalClicks: json['totalClicks'] ?? 0,
      currentWoodenFishId: json['currentWoodenFishId'] ?? 'basic',
      lastPlayDate: DateTime.fromMillisecondsSinceEpoch(
        json['lastPlayDate'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
      adHidden: json['adHidden'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GameState &&
        other.todayClicks == todayClicks &&
        other.totalClicks == totalClicks &&
        other.currentWoodenFishId == currentWoodenFishId &&
        other.lastPlayDate == lastPlayDate &&
        other.adHidden == adHidden;
  }

  @override
  int get hashCode {
    return todayClicks.hashCode ^
        totalClicks.hashCode ^
        currentWoodenFishId.hashCode ^
        lastPlayDate.hashCode ^
        adHidden.hashCode;
  }
}