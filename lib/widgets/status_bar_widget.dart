import 'package:flutter/material.dart';
import '../models/wooden_fish.dart';
import '../models/game_state.dart';

class StatusBarWidget extends StatelessWidget {
  final WoodenFish currentFish;
  final GameState gameState;
  final VoidCallback? onSettingsTap;
  final VoidCallback? onFishListTap;

  const StatusBarWidget({
    super.key,
    required this.currentFish,
    required this.gameState,
    this.onSettingsTap,
    this.onFishListTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: Row(
          children: [
            // 左侧：木鱼名称
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    currentFish.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      _buildCounterChip(
                        '今日',
                        gameState.todayClicks,
                        Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      _buildCounterChip(
                        '总计',
                        gameState.totalClicks,
                        Colors.green,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // 右侧：功能按钮
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildActionButton(
                  icon: Icons.list,
                  onTap: onFishListTap,
                  tooltip: '木鱼列表',
                ),
                const SizedBox(width: 8),
                _buildActionButton(
                  icon: Icons.settings,
                  onTap: onSettingsTap,
                  tooltip: '设置',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCounterChip(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            _formatNumber(count),
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback? onTap,
    required String tooltip,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            size: 20,
            color: Colors.black54,
          ),
        ),
      ),
    );
  }

  String _formatNumber(int number) {
    if (number < 1000) {
      return number.toString();
    } else if (number < 1000000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    }
  }
}