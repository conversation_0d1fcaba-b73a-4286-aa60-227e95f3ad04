import 'package:flutter/material.dart';
import '../models/wooden_fish.dart';
import '../models/wooden_fish_data.dart';

class WoodenFishWidget extends StatefulWidget {
  final WoodenFish woodenFish;
  final VoidCallback? onTap;
  final double size;
  final bool showAnimation;

  const WoodenFishWidget({
    super.key,
    required this.woodenFish,
    this.onTap,
    this.size = 200.0,
    this.showAnimation = true,
  });
  
  // 添加一个静态方法来从外部触发动画
  static void triggerAnimation() {
    _WoodenFishWidgetState._globalAnimationTrigger?.call();
  }

  @override
  State<WoodenFishWidget> createState() => _WoodenFishWidgetState();
}

class _WoodenFishWidgetState extends State<WoodenFishWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  
  // 全局动画触发器
  static VoidCallback? _globalAnimationTrigger;

  @override
  void initState() {
    super.initState();
    
    // 设置全局动画触发器
    _globalAnimationTrigger = _playAnimation;
    
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    // 旋转动画控制器
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // 缩放动画：点击时稍微缩小然后恢复
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
    
    // 旋转动画：轻微摆动效果
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    // 清理全局动画触发器
    if (_globalAnimationTrigger == _playAnimation) {
      _globalAnimationTrigger = null;
    }
    _scaleController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _playAnimation() {
    if (!widget.showAnimation) return;
    
    // 播放缩放动画
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });
    
    // 播放旋转动画
    _rotationController.forward().then((_) {
      _rotationController.reverse();
    });
  }

  void _handleTap() {
    _playAnimation();
    widget.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: GestureDetector(
        onTap: _handleTap,
        child: AnimatedBuilder(
          animation: Listenable.merge([_scaleController, _rotationController]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Transform.rotate(
                angle: _rotationAnimation.value,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(widget.size / 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(widget.size / 2),
                    child: _buildWoodenFishImage(),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildWoodenFishImage() {
    // 由于我们还没有实际的图片资源，先用颜色和图标代替
    return Container(
      decoration: BoxDecoration(
        gradient: _getGradientForFish(widget.woodenFish.id),
        borderRadius: BorderRadius.circular(widget.size / 2),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getIconForFish(widget.woodenFish.id),
              size: widget.size * 0.4,
              color: Colors.white,
            ),
            const SizedBox(height: 8),
            Text(
              widget.woodenFish.name,
              style: TextStyle(
                color: Colors.white,
                fontSize: widget.size * 0.08,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  LinearGradient _getGradientForFish(String fishId) {
    switch (fishId) {
      case 'basic':
        return const LinearGradient(
          colors: [Color(0xFF8D6E63), Color(0xFF5D4037)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'bronze':
        return const LinearGradient(
          colors: [Color(0xFFCD7F32), Color(0xFF8B4513)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'silver':
        return const LinearGradient(
          colors: [Color(0xFFC0C0C0), Color(0xFF808080)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'gold':
        return const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFB8860B)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'platinum':
        return const LinearGradient(
          colors: [Color(0xFFE5E4E2), Color(0xFFBCC6CC)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'diamond':
        return const LinearGradient(
          colors: [Color(0xFFB9F2FF), Color(0xFF00BFFF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'master':
        return const LinearGradient(
          colors: [Color(0xFF9C27B0), Color(0xFF673AB7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'king':
        return const LinearGradient(
          colors: [Color(0xFFFF6B35), Color(0xFFF7931E)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'rainbow':
        return const LinearGradient(
          colors: [Color(0xFFFF0080), Color(0xFF8000FF), Color(0xFF0080FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return const LinearGradient(
          colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  IconData _getIconForFish(String fishId) {
    switch (fishId) {
      case 'sheep':
        return Icons.pets;
      case 'windchime':
        return Icons.music_note;
      case 'bubble':
        return Icons.bubble_chart;
      case 'alien':
        return Icons.rocket_launch;
      default:
        return Icons.temple_buddhist;
    }
  }
}