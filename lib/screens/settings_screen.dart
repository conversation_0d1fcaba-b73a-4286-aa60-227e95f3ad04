import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../services/app_state_provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String _version = '';

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _version = packageInfo.version;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: Colors.brown.shade100,
        foregroundColor: Colors.brown.shade800,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.brown.shade50,
              Colors.orange.shade50,
            ],
          ),
        ),
        child: Consumer<AppStateProvider>(
          builder: (context, appState, child) {
            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // 音效设置
                _buildSettingsCard(
                  title: '音效设置',
                  icon: Icons.volume_up,
                  children: [
                    _buildSwitchTile(
                      title: '音效开关',
                      subtitle: '开启/关闭敲击音效',
                      value: appState.appSettings.soundEnabled,
                      onChanged: (value) {
                        appState.setSoundEnabled(value);
                      },
                      icon: appState.appSettings.soundEnabled
                          ? Icons.volume_up
                          : Icons.volume_off,
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 震动设置
                _buildSettingsCard(
                  title: '震动设置',
                  icon: Icons.vibration,
                  children: [
                    _buildSwitchTile(
                      title: '震动反馈',
                      subtitle: '开启/关闭触觉反馈',
                      value: appState.appSettings.vibrationEnabled,
                      onChanged: (value) {
                        appState.setVibrationEnabled(value);
                      },
                      icon: appState.appSettings.vibrationEnabled
                          ? Icons.vibration
                          : Icons.phone_android,
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 语言设置
                _buildSettingsCard(
                  title: '语言设置',
                  icon: Icons.language,
                  children: [
                    _buildLanguageTile(
                      title: '简体中文',
                      value: 'zh_CN',
                      currentLanguage: appState.appSettings.language,
                      onChanged: (value) {
                        appState.setLanguage(value);
                      },
                    ),
                    _buildLanguageTile(
                      title: 'English',
                      value: 'en_US',
                      currentLanguage: appState.appSettings.language,
                      onChanged: (value) {
                        appState.setLanguage(value);
                      },
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 数据管理
                _buildSettingsCard(
                  title: '数据管理',
                  icon: Icons.storage,
                  children: [
                    _buildActionTile(
                      title: '重置数据',
                      subtitle: '清除所有游戏数据',
                      icon: Icons.refresh,
                      iconColor: Colors.orange,
                      onTap: () => _showResetDialog(context, appState),
                    ),
                    _buildInfoTile(
                      title: '今日点击',
                      value: '${appState.gameState.todayClicks} 次',
                      icon: Icons.today,
                    ),
                    _buildInfoTile(
                      title: '总计点击',
                      value: '${appState.gameState.totalClicks} 次',
                      icon: Icons.all_inclusive,
                    ),
                    _buildInfoTile(
                      title: '已解锁木鱼',
                      value: '${appState.unlockedFish.length} 个',
                      icon: Icons.collections,
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 关于应用
                _buildSettingsCard(
                  title: '关于应用',
                  icon: Icons.info,
                  children: [
                    _buildInfoTile(
                      title: '应用版本',
                      value: _version.isNotEmpty ? 'v$_version' : '加载中...',
                      icon: Icons.apps,
                    ),
                    _buildActionTile(
                      title: '开发者',
                      subtitle: 'Digital Wooden Fish Team',
                      icon: Icons.code,
                      iconColor: Colors.blue,
                      onTap: () => _showDeveloperDialog(context),
                    ),
                  ],
                ),
                
                const SizedBox(height: 32),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 卡片标题
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.brown.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.brown.shade600,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.brown.shade800,
                  ),
                ),
              ],
            ),
          ),
          
          // 卡片内容
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: value ? Colors.green : Colors.grey,
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Colors.green,
      ),
    );
  }

  Widget _buildLanguageTile({
    required String title,
    required String value,
    required String currentLanguage,
    required ValueChanged<String> onChanged,
  }) {
    final isSelected = value == currentLanguage;
    
    return ListTile(
      leading: Icon(
        Icons.language,
        color: isSelected ? Colors.blue : Colors.grey,
      ),
      title: Text(title),
      trailing: isSelected
          ? const Icon(
              Icons.check,
              color: Colors.blue,
            )
          : null,
      onTap: () => onChanged(value),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color iconColor,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: iconColor,
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Colors.grey.shade600,
      ),
      title: Text(title),
      trailing: Text(
        value,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _showResetDialog(BuildContext context, AppStateProvider appState) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('重置数据'),
          content: const Text(
            '确定要重置所有游戏数据吗？\n\n这将清除：\n• 所有点击记录\n• 木鱼解锁状态\n• 游戏进度\n\n此操作不可恢复！',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                
                // 显示加载对话框
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) => const AlertDialog(
                    content: Row(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(width: 16),
                        Text('正在重置数据...'),
                      ],
                    ),
                  ),
                );
                
                // 重置数据
                await appState.resetGameData();
                
                // 关闭加载对话框
                if (context.mounted) {
                  Navigator.of(context).pop();
                  
                  // 显示成功提示
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('数据重置成功'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('确定重置'),
            ),
          ],
        );
      },
    );
  }

  void _showDeveloperDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('关于开发者'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '电子木鱼 MVP版本',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              Text('这是一款简单而有趣的电子木鱼应用，让您在忙碌的生活中找到片刻宁静。'),
              SizedBox(height: 12),
              Text('功能特色：'),
              Text('• 多种木鱼样式'),
              Text('• 音效和震动反馈'),
              Text('• 进度统计'),
              Text('• 解锁系统'),
              SizedBox(height: 12),
              Text(
                '感谢您的使用！',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('知道了'),
            ),
          ],
        );
      },
    );
  }
}