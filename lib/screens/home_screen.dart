import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state_provider.dart';
import '../widgets/wooden_fish_widget.dart';
import '../widgets/status_bar_widget.dart';
import '../widgets/ad_banner_widget.dart';
import '../models/wooden_fish_data.dart';
import 'fish_list_screen.dart';
import 'settings_screen.dart';
import 'fish_list_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AppStateProvider>(
        builder: (context, appState, child) {
          if (appState.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Stack(
            children: [
              // 背景渐变
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFFFF8E1),
                      Color(0xFFFFE0B2),
                      Color(0xFFFFCC80),
                    ],
                  ),
                ),
              ),
              
              // 主要内容
              Column(
                children: [
                  // 状态栏 - 需要淡化
                  AnimatedOpacity(
                    opacity: appState.hasStartedTapping ? 0.0 : 1.0,
                    duration: const Duration(milliseconds: 2000),
                    child: StatusBarWidget(
                      currentFish: appState.currentWoodenFish,
                      gameState: appState.gameState,
                      onSettingsTap: () => _navigateToSettings(context),
                      onFishListTap: () => _navigateToFishList(context),
                    ),
                  ),
                  
                  // 中间可点击区域
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        // 触发木鱼动画
                        WoodenFishWidget.triggerAnimation();
                        // 执行点击逻辑
                        appState.tapWoodenFish();
                      },
                      child: Container(
                        width: double.infinity,
                        color: Colors.transparent,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // 木鱼组件
                              WoodenFishWidget(
                                woodenFish: appState.currentWoodenFish,
                                onTap: () => appState.tapWoodenFish(),
                                size: 250,
                              ),
                              
                              const SizedBox(height: 40),
                              
                              // 点击提示 - 需要淡化
                              AnimatedOpacity(
                                opacity: appState.hasStartedTapping ? 0.0 : 1.0,
                                duration: const Duration(milliseconds: 2000),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 10,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    '轻触屏幕任意位置敲击木鱼',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.brown.shade600,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                              
                              // 解锁提示
                              if (appState.lockedFish.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(top: 20),
                                  child: AnimatedOpacity(
                                    opacity: appState.hasStartedTapping ? 0.0 : 1.0,
                                    duration: const Duration(milliseconds: 2000),
                                    child: _buildUnlockHint(appState),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // 底部广告区域
                  if (appState.gameState.todayClicks >= 100)
                    const AdHiddenWidget()
                  else
                    AdBannerWidget(
                      isVisible: !appState.gameState.adHidden,
                      todayClicks: appState.gameState.todayClicks,
                    ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildUnlockHint(AppStateProvider appState) {
    final nextFish = appState.lockedFish.first;
    final remaining = nextFish.unlockThreshold - appState.gameState.totalClicks;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.lock_outline,
            size: 16,
            color: Colors.orange.shade700,
          ),
          const SizedBox(width: 8),
          Text(
            '再敲击 $remaining 次解锁「${nextFish.name}」',
            style: TextStyle(
              fontSize: 14,
              color: Colors.orange.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }

  void _navigateToFishList(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const FishListScreen(),
      ),
    );
  }
}