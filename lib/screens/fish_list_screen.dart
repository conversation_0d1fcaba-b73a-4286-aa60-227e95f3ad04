import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state_provider.dart';
import '../models/wooden_fish.dart';
import '../models/wooden_fish_data.dart';
import '../widgets/wooden_fish_widget.dart';

class FishListScreen extends StatelessWidget {
  const FishListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('木鱼收藏'),
        backgroundColor: Colors.brown.shade100,
        foregroundColor: Colors.brown.shade800,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.brown.shade50,
              Colors.orange.shade50,
            ],
          ),
        ),
        child: Consumer<AppStateProvider>(
          builder: (context, appState, child) {
            return CustomScrollView(
              slivers: [
                // 统计信息
                SliverToBoxAdapter(
                  child: _buildStatsCard(appState),
                ),
                
                // 基础段位木鱼
                SliverToBoxAdapter(
                  child: _buildSectionHeader('基础段位', Icons.military_tech),
                ),
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  sliver: SliverGrid(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.8,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final basicFish = WoodenFishData.allWoodenFish
                            .where((fish) => fish.type == WoodenFishType.basic)
                            .toList();
                        return _buildFishCard(context, basicFish[index], appState);
                      },
                      childCount: WoodenFishData.allWoodenFish
                          .where((fish) => fish.type == WoodenFishType.basic)
                          .length,
                    ),
                  ),
                ),
                
                // 特别风格木鱼
                SliverToBoxAdapter(
                  child: _buildSectionHeader('特别风格', Icons.auto_awesome),
                ),
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  sliver: SliverGrid(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.8,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final specialFish = WoodenFishData.allWoodenFish
                            .where((fish) => fish.type == WoodenFishType.special)
                            .toList();
                        return _buildFishCard(context, specialFish[index], appState);
                      },
                      childCount: WoodenFishData.allWoodenFish
                          .where((fish) => fish.type == WoodenFishType.special)
                          .length,
                    ),
                  ),
                ),
                
                // 底部间距
                const SliverToBoxAdapter(
                  child: SizedBox(height: 32),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatsCard(AppStateProvider appState) {
    final unlockedCount = appState.unlockedFish.length;
    final totalCount = WoodenFishData.allWoodenFish.length;
    final progress = unlockedCount / totalCount;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.collections,
                color: Colors.brown.shade600,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                '收藏进度',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.brown.shade800,
                ),
              ),
              const Spacer(),
              Text(
                '$unlockedCount/$totalCount',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.brown.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.brown.shade400),
          ),
          const SizedBox(height: 8),
          Text(
            '总点击次数: ${appState.gameState.totalClicks}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.brown.shade600,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.brown.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFishCard(BuildContext context, WoodenFish fish, AppStateProvider appState) {
    final isUnlocked = fish.isUnlocked(appState.gameState.totalClicks);
    final isCurrent = fish.id == appState.gameState.currentWoodenFishId;
    final remaining = fish.unlockThreshold - appState.gameState.totalClicks;
    
    return GestureDetector(
      onTap: () => _handleFishTap(context, fish, appState, isUnlocked),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: isCurrent
              ? Border.all(color: Colors.orange, width: 3)
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 木鱼图片区域
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: Stack(
                  children: [
                    Center(
                      child: WoodenFishWidget(
                        woodenFish: fish,
                        size: 100,
                        showAnimation: false,
                      ),
                    ),
                    
                    // 锁定遮罩
                    if (!isUnlocked)
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.lock,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ),
                    
                    // 当前使用标识
                    if (isCurrent)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(12),
                              bottomLeft: Radius.circular(12),
                            ),
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // 信息区域
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  children: [
                    Text(
                      fish.name,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: isUnlocked ? Colors.black87 : Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    if (isUnlocked)
                      Text(
                        isCurrent ? '当前使用' : '点击切换',
                        style: TextStyle(
                          fontSize: 12,
                          color: isCurrent ? Colors.orange : Colors.blue,
                          fontWeight: FontWeight.w500,
                        ),
                      )
                    else
                      Text(
                        '还需 $remaining 次',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleFishTap(BuildContext context, WoodenFish fish, AppStateProvider appState, bool isUnlocked) {
    if (!isUnlocked) {
      _showUnlockDialog(context, fish, appState.gameState.totalClicks);
      return;
    }
    
    if (fish.id == appState.gameState.currentWoodenFishId) {
      // 已经是当前木鱼，返回主页
      Navigator.of(context).pop();
      return;
    }
    
    // 切换木鱼
    appState.selectWoodenFish(fish.id);
    
    // 显示切换成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已切换到「${fish.name}」'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showUnlockDialog(BuildContext context, WoodenFish fish, int currentClicks) {
    final remaining = fish.unlockThreshold - currentClicks;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('解锁「${fish.name}」'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              WoodenFishWidget(
                woodenFish: fish,
                size: 80,
                showAnimation: false,
              ),
              const SizedBox(height: 16),
              Text(
                '需要总计 ${fish.unlockThreshold} 次点击才能解锁',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                '当前进度: $currentClicks/${fish.unlockThreshold}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '还需要 $remaining 次点击',
                style: TextStyle(
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('知道了'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop(); // 返回主页继续敲击
              },
              child: const Text('继续敲击'),
            ),
          ],
        );
      },
    );
  }
}