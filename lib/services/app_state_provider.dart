import 'package:flutter/foundation.dart';
import 'dart:async';
import 'package:flutter/material.dart';
import '../models/game_state.dart';
import '../models/app_settings.dart';
import '../models/wooden_fish.dart';
import '../models/wooden_fish_data.dart';
import 'storage_service.dart';
import 'audio_service.dart';
import 'vibration_service.dart';

class AppStateProvider extends ChangeNotifier {
  GameState _gameState = GameState();
  AppSettings _appSettings = AppSettings();
  bool _isLoading = true;
  bool _hasStartedTapping = false;
  Timer? _hideTimer;
  
  AppStateProvider() {
    init();
  }
  
  // Getters
  GameState get gameState => _gameState;
  AppSettings get appSettings => _appSettings;
  bool get isLoading => _isLoading;
  bool get hasStartedTapping => _hasStartedTapping;
  
  // 当前木鱼
  WoodenFish get currentWoodenFish => 
      WoodenFishData.getWoodenFishById(_gameState.currentWoodenFishId) ?? 
      WoodenFishData.defaultWoodenFish;
  
  // 已解锁的木鱼列表
  List<WoodenFish> get unlockedFish => 
      WoodenFishData.getUnlockedFish(_gameState.totalClicks);
  
  // 未解锁的木鱼列表
  List<WoodenFish> get lockedFish => 
      WoodenFishData.allWoodenFish.where((fish) => !fish.isUnlocked(_gameState.totalClicks)).toList();
  
  // 初始化应用状态
  Future<void> init() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      // 初始化服务
      await AudioService.instance.init();
      await VibrationService.instance.init();
      
      // 加载保存的数据
      _gameState = await StorageService.instance.loadGameState();
      _appSettings = await StorageService.instance.loadAppSettings();
      
      // 检查每日重置
      _gameState = _gameState.checkDailyReset();
      
      // 应用设置到服务
      AudioService.instance.setEnabled(_appSettings.soundEnabled);
      VibrationService.instance.setEnabled(_appSettings.vibrationEnabled);
      
      // 保存可能更新的游戏状态（每日重置）
      await StorageService.instance.saveGameState(_gameState);
      
    } catch (e) {
      print('Failed to initialize app state: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // 点击木鱼
  Future<void> tapWoodenFish() async {
    // 标记已开始点击
    if (!_hasStartedTapping) {
      _hasStartedTapping = true;
    }
    
    // 重置计时器
    _resetHideTimer();
    
    // 检查是否有新木鱼解锁
    final oldUnlockedCount = unlockedFish.length;
    
    // 更新游戏状态
    _gameState = _gameState.incrementClicks();
    
    // 检查是否有新解锁
    final newUnlockedCount = unlockedFish.length;
    final hasNewUnlock = newUnlockedCount > oldUnlockedCount;
    
    // 播放音效和振动
    if (hasNewUnlock) {
      await VibrationService.instance.unlockVibration();
    } else {
      await VibrationService.instance.lightVibration();
    }
    
    await AudioService.instance.playCurrentFishSound(currentWoodenFish);
    
    // 保存状态
    await StorageService.instance.saveGameState(_gameState);
    
    notifyListeners();
  }
  
  // 切换当前木鱼
  Future<void> selectWoodenFish(String fishId) async {
    final fish = WoodenFishData.getWoodenFishById(fishId);
    
    // 检查木鱼是否存在且已解锁
    if (fish == null || !fish.isUnlocked(_gameState.totalClicks)) {
      return;
    }
    
    _gameState = _gameState.copyWith(currentWoodenFishId: fishId);
    await StorageService.instance.saveGameState(_gameState);
    
    notifyListeners();
  }
  
  // 更新音效设置
  Future<void> setSoundEnabled(bool enabled) async {
    _appSettings = _appSettings.copyWith(soundEnabled: enabled);
    AudioService.instance.setEnabled(enabled);
    
    await StorageService.instance.saveAppSettings(_appSettings);
    notifyListeners();
  }
  
  // 更新振动设置
  Future<void> setVibrationEnabled(bool enabled) async {
    _appSettings = _appSettings.copyWith(vibrationEnabled: enabled);
    VibrationService.instance.setEnabled(enabled);
    
    await StorageService.instance.saveAppSettings(_appSettings);
    notifyListeners();
  }
  
  // 更新语言设置
  Future<void> setLanguage(String language) async {
    _appSettings = _appSettings.copyWith(language: language);
    await StorageService.instance.saveAppSettings(_appSettings);
    notifyListeners();
  }
  
  // 重置游戏数据
  Future<void> resetGameData() async {
    _gameState = GameState();
    await StorageService.instance.saveGameState(_gameState);
    notifyListeners();
  }
  
  // 重置所有数据
  Future<void> resetAllData() async {
    _gameState = GameState();
    _appSettings = AppSettings();
    
    await StorageService.instance.clearAll();
    
    // 重新应用默认设置
    AudioService.instance.setEnabled(_appSettings.soundEnabled);
    VibrationService.instance.setEnabled(_appSettings.vibrationEnabled);
    
    notifyListeners();
  }
  
  // 重置隐藏计时器
  void _resetHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 5), () {
      _hasStartedTapping = false;
      notifyListeners();
    });
  }
  
  @override
  void dispose() {
    _hideTimer?.cancel();
    AudioService.instance.dispose();
    super.dispose();
  }
}