import 'package:vibration/vibration.dart';

class VibrationService {
  static VibrationService? _instance;
  static VibrationService get instance => _instance ??= VibrationService._();
  
  VibrationService._();
  
  bool _isEnabled = true;
  bool _hasVibrator = false;
  
  // 初始化振动服务
  Future<void> init() async {
    // 检查设备是否支持振动
    _hasVibrator = await Vibration.hasVibrator() ?? false;
  }
  
  // 轻微振动（点击反馈）
  Future<void> lightVibration() async {
    if (!_isEnabled || !_hasVibrator) return;
    
    try {
      // 使用短暂的振动作为点击反馈
      await Vibration.vibrate(duration: 50);
    } catch (e) {
      // 振动失败时静默处理
      print('Failed to vibrate: $e');
    }
  }
  
  // 中等振动（特殊事件反馈）
  Future<void> mediumVibration() async {
    if (!_isEnabled || !_hasVibrator) return;
    
    try {
      await Vibration.vibrate(duration: 100);
    } catch (e) {
      print('Failed to vibrate: $e');
    }
  }
  
  // 强振动（重要事件反馈）
  Future<void> strongVibration() async {
    if (!_isEnabled || !_hasVibrator) return;
    
    try {
      await Vibration.vibrate(duration: 200);
    } catch (e) {
      print('Failed to vibrate: $e');
    }
  }
  
  // 自定义振动模式（解锁新木鱼时使用）
  Future<void> unlockVibration() async {
    if (!_isEnabled || !_hasVibrator) return;
    
    try {
      // 三次短振动表示解锁
      await Vibration.vibrate(duration: 100);
      await Future.delayed(const Duration(milliseconds: 100));
      await Vibration.vibrate(duration: 100);
      await Future.delayed(const Duration(milliseconds: 100));
      await Vibration.vibrate(duration: 100);
    } catch (e) {
      print('Failed to vibrate: $e');
    }
  }
  
  // 设置振动开关
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }
  
  // 获取振动开关状态
  bool get isEnabled => _isEnabled;
  
  // 检查设备是否支持振动
  bool get hasVibrator => _hasVibrator;
  
  // 取消振动
  Future<void> cancel() async {
    try {
      await Vibration.cancel();
    } catch (e) {
      print('Failed to cancel vibration: $e');
    }
  }
}