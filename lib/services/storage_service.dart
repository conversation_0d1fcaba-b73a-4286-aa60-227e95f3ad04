import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/game_state.dart';
import '../models/app_settings.dart';

class StorageService {
  static const String _gameStateKey = 'game_state';
  static const String _appSettingsKey = 'app_settings';
  
  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  
  StorageService._();
  
  SharedPreferences? _prefs;
  
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
  
  // 游戏状态相关
  Future<GameState> loadGameState() async {
    await init();
    final jsonString = _prefs?.getString(_gameStateKey);
    if (jsonString != null) {
      try {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return GameState.fromJson(json);
      } catch (e) {
        // 如果解析失败，返回默认状态
        return GameState();
      }
    }
    return GameState();
  }
  
  Future<bool> saveGameState(GameState gameState) async {
    await init();
    final jsonString = jsonEncode(gameState.toJson());
    return await _prefs?.setString(_gameStateKey, jsonString) ?? false;
  }
  
  // 应用设置相关
  Future<AppSettings> loadAppSettings() async {
    await init();
    final jsonString = _prefs?.getString(_appSettingsKey);
    if (jsonString != null) {
      try {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return AppSettings.fromJson(json);
      } catch (e) {
        // 如果解析失败，返回默认设置
        return AppSettings();
      }
    }
    return AppSettings();
  }
  
  Future<bool> saveAppSettings(AppSettings settings) async {
    await init();
    final jsonString = jsonEncode(settings.toJson());
    return await _prefs?.setString(_appSettingsKey, jsonString) ?? false;
  }
  
  // 清除所有数据
  Future<bool> clearAll() async {
    await init();
    return await _prefs?.clear() ?? false;
  }
  
  // 清除游戏数据
  Future<bool> clearGameData() async {
    await init();
    return await _prefs?.remove(_gameStateKey) ?? false;
  }
  
  // 清除设置数据
  Future<bool> clearSettings() async {
    await init();
    return await _prefs?.remove(_appSettingsKey) ?? false;
  }
}