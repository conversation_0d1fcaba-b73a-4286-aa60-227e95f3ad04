import 'package:audioplayers/audioplayers.dart';
import '../models/wooden_fish.dart';
import '../models/wooden_fish_data.dart';

class AudioService {
  static AudioService? _instance;
  static AudioService get instance => _instance ??= AudioService._();
  
  AudioService._();
  
  final AudioPlayer _audioPlayer = AudioPlayer();
  final Map<String, String> _preloadedSounds = {};
  bool _isEnabled = true;
  
  // 初始化音频服务
  Future<void> init() async {
    // 设置音频播放模式
    await _audioPlayer.setPlayerMode(PlayerMode.lowLatency);
    
    // 预加载所有音效
    await _preloadAllSounds();
  }
  
  // 预加载所有木鱼音效
  Future<void> _preloadAllSounds() async {
    for (final fish in WoodenFishData.allWoodenFish) {
      _preloadedSounds[fish.id] = fish.soundPath;
    }
  }
  
  // 播放指定木鱼的音效
  Future<void> playWoodenFishSound(String fishId) async {
    if (!_isEnabled) return;
    
    try {
      final soundPath = _preloadedSounds[fishId];
      if (soundPath != null) {
        await _audioPlayer.play(AssetSource(soundPath.replaceFirst('assets/', '')));
      }
    } catch (e) {
      // 音效播放失败时静默处理，不影响用户体验
      print('Failed to play sound for fish $fishId: $e');
    }
  }
  
  // 播放当前木鱼音效
  Future<void> playCurrentFishSound(WoodenFish fish) async {
    await playWoodenFishSound(fish.id);
  }
  
  // 设置音效开关
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }
  
  // 获取音效开关状态
  bool get isEnabled => _isEnabled;
  
  // 停止当前播放
  Future<void> stop() async {
    await _audioPlayer.stop();
  }
  
  // 设置音量
  Future<void> setVolume(double volume) async {
    await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
  }
  
  // 释放资源
  Future<void> dispose() async {
    await _audioPlayer.dispose();
  }
}