// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:digital_wooden_fish/models/game_state.dart';
import 'package:digital_wooden_fish/models/app_settings.dart';

void main() {
  // 专注于单元测试，跳过复杂的UI测试
  // UI测试需要更复杂的设置和模拟
  
  group('GameState tests', () {
    test('GameState click increment test', () {
      final gameState = GameState();
      final newState = gameState.incrementClicks();
      
      expect(newState.todayClicks, 1);
      expect(newState.totalClicks, 1);
    });
    
    test('GameState daily reset test', () {
      // Create a game state with yesterday's date
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      final gameState = GameState(
        todayClicks: 50,
        totalClicks: 100,
        lastPlayDate: yesterday,
      );
      
      final resetState = gameState.checkDailyReset();
      
      expect(resetState.todayClicks, 0);
      expect(resetState.totalClicks, 100); // Total should remain
      expect(resetState.adHidden, false); // Ad should be visible again
    });
  });
  
  group('AppSettings tests', () {
    test('AppSettings default values test', () {
      final settings = AppSettings();
      
      expect(settings.soundEnabled, true);
      expect(settings.vibrationEnabled, true);
      expect(settings.language, 'zh');
    });
  });
}
